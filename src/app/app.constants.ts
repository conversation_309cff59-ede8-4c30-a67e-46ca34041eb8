// API
import { PERMISSIONS_LIST as PLIST, PERMISSIONS_NAMES as PNAMES } from '@skywind-group/lib-swui';
import { ReplaySubject } from 'rxjs';
import { environment } from '../environments/environment';
import { SelectOptionModel } from './common/models/select-option.model';
import { Currency, Language } from './common/typings';
import { FinishedOption } from './common/typings/finished-option';
import { GameProvider } from './pages/games-management/game-provider.model';

console.log('environment', environment);
export const FORMAT_DATETIME = 'DD.MM.YY HH:mm';

export const APP_VERSION = environment.APP_VERSION;
export const API_ENDPOINT = environment.ENV_API_SERVER_ENDPOINT;
export const API_ENDPOINT2 = environment.ENV_API_SERVER_ENDPOINT2;
export const BASE_URL = environment.ENV_BASE_URL;

export const MESSAGE_DISPLAY_DURATION = 5000; // 5 sec in milliseconds
export const TOKEN_REFRESH_TIMEOUT = 600000;
// export const USER_LOGGED_DURATION = 300000;

export const FORM_RENDERER_FRAMEWORK = 'bootstrap-3';
export const FORM_DEFAULT_PROPERTY_PARAMS = {
  // type: 'string',
  maxLength: 255
};

export const FORM_DEFAULT_OPTIONS = {
  addSubmit: false, // Add a submit button if layout does not have one
  loadExternalAssets: false, // Load external css and JavaScript for frameworks
  formDefaults: { feedback: true }, // SHow inline feedback icons
  debug: false,
};

// Graylog
// export const GRAYLOG_API_ENDPOINT = environment.ENV_GRAYLOG_API_ENDPOINT;

// APP
export const DEBUG = environment.ENV_DEBUG === 'true';
export const STORAGE_ACCESS_TOKEN_NAME = 'accessToken';
export const STORAGE_ACCESS_TOKEN_DATE = 'accessTokenDate';
export const STORAGE_USER_PROFILE_NAME = 'user';
export const STORAGE_ACCESS_KEY_ACTIVE = DEBUG ? 'swEntityActiveKey' : 'swA';
export const STORAGE_LAST_USER_ACTION = 'lastUserAction';
export const STORAGE_IS_LOGGED_OUT = 'isLoggedOut';

// export const INITIATOR_SERVICE_NAME = 'Backoffice';

export const ZERO_TIME = '0001-01-01T00:00:00.000Z';


export const PERMISSIONS_NAMES: Record<string, string> = {
  ...PNAMES,
};

export const PERMISSIONS_LIST = {
  ...PLIST,
};

export const FORMAT_DATETIME_FULL = 'DD/MM/YYYY HH:mm:ss.SSS';
// export const FORMAT_DATETIME_SHORT = 'DD/MM/YYYY HH:mm:ss';
export const FORMAT_DATETIME_SMALL = 'DD/MM/YY HH:mm:ss';
// export const FORMAT_DATETIME_SMALL_NO_SECONDS = 'DD/MM/YY HH:mm';
export const FORMAT_DATE = 'DD.MM.YYYY';
export const FORMAT_DATE_MONTH_SHORT = 'MM/YYYY';
// export const FORMAT_TIME = 'HH:mm:ss';

export const COUNTRY_LIST$ = new ReplaySubject<SelectOptionModel[]>(1);
export const CURRENCY_LIST$ = new ReplaySubject<Currency[]>(1);
export const LANGUAGES_LIST$ = new ReplaySubject<Language[]>(1);
export const GAME_PROVIDERS_LIST$ = new ReplaySubject<GameProvider[]>(1);

export const setCurrencyList = (list) => {
  CURRENCY_LIST$.next(list);
};

export const setCountriesList = (list) => {
  COUNTRY_LIST$.next(list);
};

export const setLanguagesList = (list) => {
  LANGUAGES_LIST$.next(list);
};

export const setGameProvidersList = (list) => {
  GAME_PROVIDERS_LIST$.next(list);
};

// export const CDN_LIST = require('./mock-data/images.json');
export const tagClassMap = {
  'platform': 'sw-chip sw-chip-green',
  'class': 'sw-chip sw-chip-blue',
  'provider': 'sw-chip sw-chip-green',
  'feature': 'sw-chip sw-chip-purple',
};
export const providerCodeClassMap = {
  'SW': 'sw-chip sw-chip-green',
  'PT': 'label bg-teal',
  'FLC-PT': 'label bg-teal-800',
  'FLC2': 'label bg-blue-800',
  'QS': 'label bg-slate-300',
  'quickspin': 'label bg-slate-300',
  'EC': 'label bg-info-800',
  'eyecon': 'label bg-info-800',
  'DEFAULT': 'sw-chip',
};
export const gameStatusClassMap = {
  normal: 'sw-chip-green',
  suspended: '',
  test: 'sw-chip-blue',
  hidden: 'sw-chip-orange',
};

export const ACCOUNT_TYPE_CLASS_MAP = {
  'true': 'sw-chip',
  'false': 'sw-chip sw-chip-green'
};

export const FINISHED_CLASS_MAP = {
  'false': 'sw-chip',
  'true': 'sw-chip sw-chip-green',
};

export const ACCOUNT_TYPE_MAP = {
  'true': 'GAMEHISTORY.GRID.testAccount',
  'false': 'GAMEHISTORY.GRID.realAccount'
};

export const DEVICE_MAP = [
  { id: 'mobile', code: 'mobile', displayName: 'GAMEHISTORY.GRID.onlyMobile' },
  { id: 'web', code: 'web', displayName: 'GAMEHISTORY.GRID.onlyWeb' },
];

export const FINISHED_MAP = [
  { id: 'true', code: 'true', displayName: 'GAMEHISTORY.GRID.onlyFinished' },
  { id: 'false', code: 'false', displayName: 'GAMEHISTORY.GRID.onlyUnfinished' },
];

export const RECOVERY_TYPE_MAP = [
  { id: 'force-finish', code: 'force-finish', displayName: 'GAMEHISTORY.GRID.force-finish' },
  { id: 'finalize', code: 'finalize', displayName: 'GAMEHISTORY.GRID.finalize' },
  { id: 'null', code: 'null', displayName: 'GAMEHISTORY.GRID.null' },
];

export const IS_TEST_MAP = [
  { id: 'true', code: 'true', displayName: 'GAMEHISTORY.GRID.onlyTest' },
  { id: 'false', code: 'false', displayName: 'GAMEHISTORY.GRID.onlyReal' },
];

export const DEFAULT_PAGE_SIZE = 20;
export const PAGE_PAGINATION_MAX_ITEMS = 4;
export const PAGE_PAGINATION_BEFORE_CURRENT = 2;

export const WEEK_DAYS = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

export enum GridOrderDirectionEnum {
  NONE = 0,
  ASC = 1,
  DESC = 2,
}

export enum SchemaFieldMatchEnum {
  Equals = 0,
  Contains = 1,
  NotContains = 2,
  GreaterThan = 3,
  LessThan = 4,
  In = 5,
  GreaterThanEquals = 6,
  LessThanEquals = 7,
  Ne = 8,
}

export const SCHEMA_FILTER_FIELD_POSTFIX_MAP = [
  '',
  '__contains',
  '__contains!',
  '__gt',
  '__lt',
  '__in',
  '__gte',
  '__lte',
  '__ne',
];


// Localization
export const LOCALE_LIST = [
  {
    id: 'en',
    dialect: 'en',
    title: 'LANGUAGE.English',
    // tslint:disable-next-line
    image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAflJREFUeNpinDRzn5qN3uFDt16+YWBg+Pv339+KGN0rbVP+//2rW5tf0Hfy/2+mr99+yKpyOl3Ydt8njEWIn8f9zj639NC7j78eP//8739GVUUhNUNuhl8//ysKeZrJ/v7z10Zb2PTQTIY1XZO2Xmfad+f7XgkXxuUrVB6cjPVXef78JyMjA8PFuwyX7gAZj97+T2e9o3d4BWNp84K1NzubTjAB3fH0+fv6N3qP/ir9bW6ozNQCijB8/8zw/TuQ7r4/ndvN5mZgkpPXiis3Pv34+ZPh5t23//79Rwehof/9/NDEgMrOXHvJcrllgpoRN8PFOwy/fzP8+gUlgZI/f/5xcPj/69e/37//AUX+/mXRkN555gsOG2xt/5hZQMwF4r9///75++f3nz8nr75gSms82jfvQnT6zqvXPjC8e/srJQHo9P9fvwNtAHmG4f8zZ6dDc3bIyM2LTNlsbtfM9OPHH3FhtqUz3eXX9H+cOy9ZMB2o6t/Pn0DHMPz/b+2wXGTvPlPGFxdcD+mZyjP8+8MUE6sa7a/xo6Pykn1s4zdzIZ6///8zMGpKM2pKAB0jqy4UE7/msKat6Jw5mafrsxNtWZ6/fjvNLW29qv25pQd///n+5+/fxDDVbcc//P/zx/36m5Ub9zL8+7t66yEROcHK7q5bldMBAgwADcRBCuVLfoEAAAAASUVORK5CYII=',
  },
  {
    id: 'zh',
    dialect: 'zh-cn',
    title: 'LANGUAGE.Chinese',
    // tslint:disable-next-line
    image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAiYAAAFvCAYAAAB+cQB/AAAZTklEQVR4nO3d+XtdV33oYQ22ZEm2pH+jToCklCFhKKTl9lKGFGjLUIZCoRSeXrgByqVlCGW4ZSplKDRhaCCFh9vkFlqmptBAmRqGhoSQFoLtSLItz5Ns2ZasYVVrK3Is65xjSeccrbXPeX94f+Fxkr2P/HA+2mvt7+q4/5eGAwBADjpSXwAAwBJhAgBkQ5gAANkQJgBANoQJAJANYQIAZEOYAADZECYAQDaECQCQDWECAGRDmAAA2RAmAEA2hAkAkA1hAgBkQ5gAANkQJgBANoQJAJANYQIAZEOY0JZGLk9/DQCsJExoO3ufui0ce9uW5NcBwErChLYydtVQmPtpZzj8mv7k1wLASsKEtjFyxVCY/rfuEHZ1hP3P3Zr8egBYSZjQHi4bDqc/tzmE+zsKe5+8Lf01AbCCMCF7ux8/WPdm1YkP9p6Pkigu6aS+LwBWEiZkb+zqwTBzR1c48b7edQXF0T/tWxYlYWdHuN9bOQBZEiaUwpE39BV7Q8KOjnDqb3vC+LWrW4o5+AcDRYhcGCazd3Umvx8AKhMmlMP24TB9e/eywJi6rTscemV/GHlI5X9m/Le2hfmfdS5/WrIgboBNfj8AVCRMKI3xp21bfGpy/8onIMffuSWMPXbw/J/dfc1g8b9f/Gejs1/YlPxeAKhMmFAqJ/+6t2JsRPM7O8LkpzaH/c8fCOe+3VX1z03e1JP8PgCoTJhQKqOPHAqzd1d+ErJaEx/oTX4fAFQmTCidw/+7v64wOfZW4+gBciVMKJ/tw2Hqn7vXHSYxbJLfAwAVCRNKqdgIu3N9YTLzw65w5LV9YeRKQ9YAciNMKK2JGhthVyMe5jfx/t6w55rB5PcCwCJhQmmNPmLxpOB64qSwqyOcvmXxbZ64TJT6vgDamTDZQKNXpL+GVjL2mMEw95MGhMkFzn2nKxz5P31h9OGWeQBSECYb6NSNPX4jb5DRK4fC9NfXvwH2Uubv6wiTn94c9vwPpxADbCRhslEuGy6WHfb9ztb011J2lw8XSy/NipLCzsVNsqc/J04ANpIw2SD7nr21+MI7+VFTR+tVa/prPRthz9y6KRz78y1h33O2emMHIBFhskEmPrT4ZVqcbGs5Z92OvqmvISEyc0dXcUpxfG14729uK55opb43AITJxlgIkdk7Hzy7JZ56m/yaSujgywYqHuK3Fmc+v2nZYX8A5EWYbIB9z9y67MvxxHud1bKezzBuSK33Scnx/2scPUDOhMkGOPH+5XsiZv69K/k1lUkcgFZrXsmFyzKHXtFf86nKkdf3Jb8fAKoTJhtg5vtdK74g9z7Vcs5qjD1qqNiXc/6z29ERpr7WXZwQHJd2Ki3LnLyhp2qYxH8m9T0BUJ0wabLxp2+rvKTwF5YULiW+GTN1W3eY/mZ3OP7OLWH/s1f3tkyMmbl7Kj9h8bo2QN6ESZMdf/eWil+Q577dnfzacjdyxdC635aJyzqVPnczSQDyJkya7Nx3Vy7jnP+S/A1fkk2zEDTTX1s5GTaer5P82gCoSpg00d4nV17GWXLsbZZzmmn8GVuXbYSd/0VH8msCoDZh0kTH31F5GWfJ9O2Wc5rt5I0PboSd/ZG3oQByJ0yaKG7avNRcjfgqbOrrbGVjjx4Kc/cuboSNP4/U1wNAbcKkSfb8+uCqBn4dfbO5Gs125HWLG2HPfnlT8msBoDZh0iTHrq+9jLMkvg6b+lpb3mXDYeYHXeHM/xcmALkTJk0y/fVLL+Ms2f14yznNFmegTH56c/LrAKA2YdIEu5+wumWc82PS32A5ZyOYYQKQP2HSBEffWHm4VzVnv2SJAQAiYdIEU1/dtKYwibM2xq62nAMAwqTBxh4zWPN026rLOa+znFN624fDib/sTX8dACUmTBrsyOvXtoyz5MznLee0gjjELQ7WS30dAGUlTBrs7D+tcRlnaVz6jo4w+kjnuJTd1FcWf/4xUFNfC0AZCZMGilNGw861R8mSw6/uT34P1OfE+3sf/HleV//Ps3iV/PL09wWwUYRJA8UvovVGSXT6783ZKLulKbNLm5oP/fH64iTuVZq8qad4Apf6ngA2kjBpoDhZtJ4wiaffjv6K5Zwy2//crct/rjs7wsGXD6z6n9/9uMEw8aHeML+jswibeEJ16nsC2EjCpEFGHzFUhEU9YRIdeqXlnDLb/asrh+vF/UMHXlI7TuI/d/IjPcv+Dk3e7Aka0H6ESYPER/b1RkmxnPNZX0altn04zN9X+WnYgReujJPdT1wIkht6VkbtQszECcLJ7wdggwmTBjn9uc0NCZP5n3eGkSst55TZuW9VPicp/mzjUk/8M/H06VMf7ymeplT6sxMfNg8FaE/CpAFGf3mo4m/J63XwZavfk0B+akVqjJM4s6bW21vzP+s0CRhoW8KkAQ6+vDHLOOf3FnzKck6Zxemv9fz8j7/LgDagfQmTBoibFBsZJnP/2RlGHmY5p6yWvTK81p/9PZ3ezALamjCpUwyI+f/qbGiYRAd+33JOWa14ZXgNjr3F0xKgvQmTOsXXQBsdJVHcGJn63li7fc/aGs5+aX3zbGbv7AojV3haArQ3YVKnU5/saUqYzP20M4w8JP39sTr7nrMQJF9YZZCMVP7fnTANIEzqEsNh7t7GL+Ms2f97W5PfI7Xtf95CkHy5vom/hZ0dxSvEqe8HIDVhUoc4MKtZURKd/KjlnFztf8FAmPpqA4LkAjN3dBUj6VPfG0BKWYdJPCckfvnve/bWLJ2+pbFfTJWWc+ISQer7rCQ+Kdj//PZ7ohM3JU/fXnmAWiOc+15XGHusOAHaV9ZhEjcCxoPxqk3HJJGFn8fMD7vC3qe0yQFz24fD0Tf2FZtT1/2Z7VpDnHyrO4xdZRMs0J6yDpMlB1860JRXclmfiQ/0hpHL0/+92Ei7Hz8YDrxooHidN254jk9N4hTXZn3G099ciJNHiROg/ZQiTKK49r7e1zBpjNm7Om3IvdDli2fexHA+9o4tYfLvNi+ek9OgJ3zT/9pdnFqd/D4BNlBpwqSw8EUQvwAa9X/8rN6ZWzdZXlilkYcOh/Gnb6t5Hs5qTf3LQpw83OcOtI9yhckD4hCruMch9Zd1O5j/RUexvyLus0j9cy+bme/V+Du6q6NYDoobnFcVJ06cBtpEKcMkio+4Jz/T2DNqWC6+ITL+tDbZ4NoEceN2tc928qYHXwWPy5Tx9eOjb+oLpz7RE6a/ftH+lV2Le05MhQXaQWnDZMnh1/QXx8Sn/hJvNXEkvt/S6zPxocqnDM/HYWrXXOKV4Lh/5UmL+1eO/8WW4qlJPCwyLhOlvi+AZip9mER7f2Nb8Rtl6i/zVhBPNj70iv7kP9NWcOQNlU8ZPvkRg/MAqmmJMIniKb8TH678GyqrE38r3/NEw70apdJk4Ph0zwA1gOpaJkyWxNN+535iaWdNdnWEE+9tv9kkzRZfJb74sz7xnt7k1wWQs5YLk2j3wm+kZ79o5slqxGmmcbx86p9ZK4qhd+Gr7fENHHNJAGpryTApXDYcjr3dzJNazty6OYw92hdlM8WD+ZY+72PXb0l+PQC5a90weUAx8+QHZp5cqJhN8mdmk2yEM/+4+ORu9u5Or/sCrELLh0k0+itDxbjw1EGQg3Pf7Qp7zSbZMCdv7Ck+9yOv60t+LQBl0BZhsqTdZ56c+pjZJBstLifO3dNpYzHAKrVVmER74syTb7TXzJO5e80mSeXwq/vD0Td7WgKwWm0XJlEx86TKVM5WM3Vbd9h9qSmjNM3Y1YPFRuzU1wFQFm0ZJksOvHggzN3doks7cTbJu80mAaBc2jpMorHHDYaz/9RaM0+K2STPNZsEgPJp+zApxJknf94aM09O32I2CQDlJUwusO+ZW8PM98s586SYTfKnZpMAUG7C5CLFzJObyzXz5Nx3usLep5pNAkD5CZMqDl/XH+Z/nv/G2FM39oQRs0kAaBHCpIZjb92SPDxqifNYUn9GANBIwqSG03+f95LO/I6OMPpIT0sAaB3CpIq41yRuKE0dH5dy+FUmugLQOoRJFYde2Z88OlYjPtVJ/VkBQKMIkypOfzbvZZzzyzn3dYTRh1vOAaA1CJMK4lsuZXgjZ4kD+gBoFcKkgoMvG0geG2sx+RnLOQC0BmFSweSnyrGMc34552edYeQKyzkAlJ8wucjIw4bC3H+WZxlnycGXDiT/7ACgXsLkIgdeXK5lnCWTn+pJ/tkBQL2EyUVOfaIneWSsR3zKM/LQ9J8fANRDmFxg5CHDYe6n5VvGWXLg9y3nAFBuwuQC+59fzmWcJac+bjknZ/Ftr9TXAJA7YXKBk39TzmWcJXP3dBZPfVJ/jqwUjzg4992u5NcBkDthsuSy4TB7d3OXcTbibZ/9v7c1/WfJCsVsnB0dYfzabcmvBSBnwuQB+569tbnLLJ/oCaO/PBQOv7o/zP9X8wLl5Ect5+RoaVP1xId6k18LQM6EyQMmPtzbtKck8UDAC/9be540GKb/tbsp/73ZH3cWT39Sf55cYPtwmL2ra/Hvg+U2gJqESRS/OP6jq+GRMPUv3WHPrw1W/G/GV3snPtCcGNr3u5ZzcrL3KduW/Xy8PQVQnTBZsO9ZDV7G2dURTryvd1W/GR940UDD97ZMfNByQU6OXb9l2c9n8mZnGwFUI0wWnHh/455cxKWUtW5AHXvMYDjzhU2Nu4YfdRVPgVJ/riw6+6XlP9v5+zqKt3RSXxdAjoTJgpnvN2YZ58ytm8PYVev8wtm++Jv1/I7GxMn4Myzn5CAGSKWf6eHr+pNfG0CO2j5Mxp++re4ImP9FRzj6xr6GPKWIQTFzR/2hFJeSUn+2PPCacIWfT3yKkvraAHLU9mFy/N1b6gqAODRr/GmNnU0x+vCh4lC+eq4rxk3qz5bhcPLGKj/HXR1h9xMqb4wGaGdtHyYxLNb75R9HwMfZJM26tsOvqm/mSaODibWr9bbXsbdtSX59ALlp6zDZ+5vrW8apNJukWYqZJ19f38yT4+/yxZdKfB1832/Xfttr9s7OYuDekdf3haNv6itC5cR7eovXyOOgvFOf7AmTf7c5nPn85jD9je7iKdipj/WE+81BAVpYW4fJ8XeufRln6rbusOeajX0EH187nvirtb85dO47lnMaJc4iOfMPm8LZr2wq9oec/eKmMPXVTWH69u7ic575QVfx2nfxhGvX+pfgaol/B7xtBbS6tg6T6W+u4UlEnE3yntXNJmmWAy8cCLN3rW1pZ+//tJzTKHFpLAZIM6LjUk/o4s8+9f0DbIS2DZO4RLLaL4bZO7vC/ufl8fptMfPk86ufeXLs7ZZzGimGaXzSNr9zY6IkPpHZ/USbZIH20bZhcuytq1vGOXNLHbNJmiXOPHnL6maexL0Jya+3BcWnJ2t64rYO8eC/kYdl9ncPoMnaNkwutaG0mE3yZ42ZTdIs47+1Lcz8+6WXFqqd10N94gbXuLwXGvz0JP7dO/InfcnvDyCFtgyTOD+i1hdDfIV471PLsTcjzjw5dVPtmSdxomzq62xlMRDPfbsxe0/i68X7npnHsiFACm0ZJnFKa9XH502eTdIsh/5X9ZknU1+znNNsccnlxF/21vVGTnzTJ7tlQ4AN1pZhEl/zvPhLYe7eznDoFeU+v2TPr1efebL7Vy3nbIQ4u2Q9p0XHJyUjl6e/foDU2i5Mxh47uOK32jibpFXefIhvjRSnJV90j8V+mQyurx2c/fL6Toq2FwigDcMkTtk8/2WQwWySZtn/guUzT6Zuc2jcRqh2mvBqxNeQU18/QGptFyZLSx2zd3eF/c9t7U2GY1cvn3kSZ6CkvqZWV+004dWIb1ilvn6A1NoqTOLGwvgqZrHJ8NFtsskwzjy5vi+Ehd/i4+yW5NfT4k7eUN+p0HGPSup7AEiprcIkvjZ74r3ted5I/MI7c6vlnGardZrw3D2d4cw/bK4ZJvHwvtT3AJBSW4VJnPmR+hrcf+uKB/1VC474yvbSm1EHXjRQNWDmftpZDG5LfS8AqbRVmEAzxSdyFZ+C3LBytPzoI4bCqU9WXvaJ+1RS3wtAKsIEGuTsl5a/Jjx/X0c4fF3t2TgHXjIQZn+8fO7J6f+3Ofm9AKQiTKAB4mvCF56ZM/ODrjB+7eqONRh71FCYvPnBvSfx5OK22ZwNcBFhAg1w8A8ffE04vqIdY2M9/444gTjO1zn6JgPxgPYkTKAB4vJLjJIT7+sN91+2/n9PfKU9vs4+c4eZJkB7EiZQr+3DYeaHXeHgHzXurKV40OSeFjkmAWAthAnUKZ6/tNr9JADUJkwAgGwIEwAgG8IEAMiGMAEAsiFMaDvH3u6UZYBcCRPaytE394Vz3+pOfh0AVCZMaBv7nrW1GBs/9ZVNya8FgMqECW1h7HGDYfbuxcPyJj/tkDyAXAkTWt7IQ4fD1D93nz/L5uRHepJfEwCVCROyt/sJ9Y1mP3lDz/koOX+eTQb3BcBKwoTsnXh3bzj3na5w+Lr+MHrl2k7tPfzavmVREh33Vg5AtoQJ+btsuHiTJkbF/M87w6kbe8K+391aHJ5X65+Lm13nd3SsCJN4QF7yewKgImFCKex50uCKyJj5Xlc49rYtYffjVy71xIP1Zu/sWhEl0eHXNO4UYAAaS5hQGkffsnJZprCrI5z5wqZw6BX9YeSKoTDykOWbXS8W/1zqewGgMmFCeVy+EBxfqx4c0dy9neeXfao58OKB9PcCQEXChFLZ++RtFfeNrMX+52xNfh8AVCZMKJ34Vk09YTJ+7bbk9wBAZcKE0hm5fDhMf6P2ck0tp27qCeNPFycAORImlFIMi1Dnks70v3UXrw6PXbW22SgANI8wobTi4LV6wmTJ/M6OcPpzm8PBPxgonsakvi+AdiZMKK2Rhw1VnVWyXnM/6QwTH+gNe59iqQcgBWFCaY0/o/Jk10aZ/VFXmPir3rD7cfWd1QPA6gkTSmns6sEiHJoWJT/uDKc/u7k4n2fsUfagAGwUYULpxH0gZ7+8qbFLOPcuhsiR1/aFPU/0hAQgFWFC6Uz8dQM2ve7oKMbWH3/HluKwv5HL0t8XAMKEkjn8qv76lmju7gwHXzoQRh9heQYgR8KE0oizS+bvq+9Jydkvbkp+HwBUJ0wohbFHD116s+uujnDue7X/zOlbNie/FwCqEyZkL+7/mPpq5c2u8e2ZU5/oCYde2V/ES/zzpz7eUzVMJm8WJgA5EyZkL4bHUljEuSVn/nFTOPrmvsUhaNtX/vm4f6Ta4LWTH+tJfj8AVCdMyNqx67eE2f/oKoLiwAsGwsiVq9u0euAlAxXDZOKDvcnvCYDqhAlZq2fq6uRNK5d0jr9rS/J7AqA6YULLihNbZ+/qXBYm8QlM6usCoDphQks7+LLlSzpH3tCX/JoAqE6Y0PImP7P5fJgcfnV/8usBoDphQssbu2qoOAsnhsnBlw8kvx4AqhMmtIU452R+Z0c48EJhApAzYULbmL69O+x/3tbk1wFAdcKEtrHnmsEwfu225NcBQHXCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAbwgQAyIYwAQCyIUwAgGwIEwAgG8IEAMiGMAEAsiFMAIBsCBMAIBvCBADIhjABALIhTACAbAgTACAb/w3g1fOLvxQ5aAAAAABJRU5ErkJggg==',
  },
];

// export const LOCALE_DEFAULT = 'en';
export const STORED_LOCALE_KEY = 'locale';

export const MENU_COLLAPSE_STATE_KEY = 'isMenuCollapsed';

export const FINISHED_OPTIONS_LIST: FinishedOption[] = [
  {
    id: 'forceFinish',
    title: 'GAMEHISTORY.INTERNAL.forceFinish',
    permissions: [
      PERMISSIONS_NAMES.ENTITY_GAMECLOSE_FORCEFINISH,
      PERMISSIONS_NAMES.KEYENTITY_GAMECLOSE_FORCEFINISH,
    ]
  },
  {
    id: 'revert',
    title: 'GAMEHISTORY.INTERNAL.revert',
    permissions: [
      PERMISSIONS_NAMES.ENTITY_GAMECLOSE_REVERT,
      PERMISSIONS_NAMES.KEYENTITY_GAMECLOSE_REVERT,
    ]
  },
  {
    id: 'retryPending',
    title: 'GAMEHISTORY.INTERNAL.retryPending',
    permissions: [
      PERMISSIONS_NAMES.ENTITY_GAMECLOSE_RETRY,
      PERMISSIONS_NAMES.KEYENTITY_GAMECLOSE_RETRY,
    ]
  },
  {
    id: 'requireTransferOut',
    title: 'GAMEHISTORY.INTERNAL.requireTransferOut',
    permissions: [
      PERMISSIONS_NAMES.ENTITY_GAMECLOSE_TRANSFER_OUT,
      PERMISSIONS_NAMES.KEYENTITY_GAMECLOSE_TRANSFER_OUT,
    ]
  },
  {
    id: 'finalize',
    title: 'GAMEHISTORY.INTERNAL.finalize',
    permissions: [
      PERMISSIONS_NAMES.ENTITY_GAMECLOSE_FINALIZE,
      PERMISSIONS_NAMES.KEYENTITY_GAMECLOSE_FINALIZE,
    ]
  },
  {
    id: 'manualFinalize',
    title: 'GAMEHISTORY.INTERNAL.manualFinalize',
    permissions: [
      PERMISSIONS_NAMES.ENTITY_GAMECLOSE_FINALIZE,
      PERMISSIONS_NAMES.KEYENTITY_GAMECLOSE_FINALIZE,
    ]
  }
];

export const OUTCOME_MAP = [
  { id: 'WIN', title: 'GAMEHISTORY.GRID.labelWin', class: 'sw-chip sw-chip-green' },
  { id: 'LOSE', title: 'GAMEHISTORY.GRID.labelLose', class: 'sw-chip sw-chip-red' },
  { id: 'TIE', title: 'GAMEHISTORY.GRID.labelTie', class: 'sw-chip sw-chip-blue' },
  { id: 'VOID', title: 'GAMEHISTORY.GRID.labelVoid', class: 'sw-chip' },
];

export const STATUS_MAP = [
  { id: 'broken', title: 'GAMEHISTORY.GRID.broken' },
  { id: 'unfinished', title: 'GAMEHISTORY.GRID.unfinished' },
  { id: 'require_logout', title: 'GAMEHISTORY.GRID.requireLogout' },
  { id: 'brokenIntegration', title: 'GAMEHISTORY.GRID.brokenIntegration' },
  { id: 'finalizing', title: 'GAMEHISTORY.GRID.finalizing' },
];

export const HISTORY_TYPE_MAP: SelectOptionModel[] = [
  {
    id: 'general',
    text: 'GAMEHISTORY.TYPE.gameHistoryLog',
    data: {
      link: './'
    }
  },
  {
    id: 'unfinished',
    text: 'GAMEHISTORY.TYPE.unfinishedBroken',
    data: {
      link: './unfinished'
    }
  },
];
