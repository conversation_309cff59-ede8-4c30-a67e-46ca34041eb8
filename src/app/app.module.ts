import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { APP_INITIALIZER, Injector, NgModule } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {
  ColumnsManagementDataProvider,
  hub_config_init,
  PERMISSIONS_NAMES,
  SwBrowserTitleService,
  SwDexieColumnsDataProviderService,
  SwDexieModule,
  SwHubAuthService,
  SwHubConfigService,
  SwHubEntityDataSource,
  SwHubInitModule,
  SwuiGridModule,
  SwuiNotificationsModule,
  SwuiTopMenuModule
} from '@skywind-group/lib-swui';
import { ToastrModule } from 'ngx-toastr';
/*
 * Platform and Environment providers/directives/pipes
 */
// App is our top level component
import { AppComponent } from './app.component';
import { AppRoutingModule } from './app.routing';
import { ActionListWidgetModule } from './common/components/schema/grid/widgets/td/action-list/action-list.widget.module';
import { TdActionListWidget } from './common/components/schema/grid/widgets/td/action-list/action-list.widget';
import { TDGameFinishWidget } from './common/components/schema/grid/widgets/td/game-finish/game-finish.widget';
import { GameFinishWidgetModule } from './common/components/schema/grid/widgets/td/game-finish/game-finish.widget.module';
import { TDRadioButtonWidget } from './common/components/schema/grid/widgets/td/radio-button/radio-button.widget';
import { RadioButtonWidgetModule } from './common/components/schema/grid/widgets/td/radio-button/radio-button.widget.module';
import { BaThemeSpinner } from './common/services/baThemeSpinner/baThemeSpinner.service';
import { BiService } from './common/services/bi.service';
import { EntityDataSourceService } from './common/services/entity-data-source.service';
import { EntityService } from './common/services/entity.service';
import { UserService } from './common/services/user.service';
import { GlobalState } from './global.state';
import { IssueWidgetComponent } from './common/components/schema/grid/widgets/td/issue/issue.widget.component';
import { IssueWidgetModule } from './common/components/schema/grid/widgets/td/issue/issue.widget.module';
import { TDBrokenGameFinishWidget } from './common/components/schema/grid/widgets/td/broken-game-finish/broken-game-finish.widget';
import {
  BrokenGameFinishWidgetModule
} from './common/components/schema/grid/widgets/td/broken-game-finish/broken-game-finish.widget.module';

/**
 * `AppModule` is the main entry point into Angular2's bootstraping process
 */
@NgModule({
  bootstrap: [
    AppComponent
  ],
  declarations: [
    AppComponent
  ],
  imports: [
    CommonModule,
    BrowserAnimationsModule,
    ToastrModule.forRoot(),
    HttpClientModule,
    SwHubInitModule.forRoot({
      name: 'casino',
      permission: PERMISSIONS_NAMES.HUB_CASINO,
      langs: [
        {
          id: 'en',
          title: 'LANGUAGE.English',
          image: 'img/flags/gb.png',
        },
        {
          id: 'zh',
          dialect: 'zh-cn',
          title: 'LANGUAGE.Chinese',
          image: 'img/flags/zh.png',
        },
      ],
      defaultLang: 'en',
      logo: 'img/logo-skywind.png',
      logoSymbols: 'img/logo-white.png',
      auth: {
        blacklistedRoutes: [
          '/api/config',
          new RegExp(/\/locale\/.*/),
          new RegExp(/\/widgets\/.*/)
        ]
      },
      lastLocationExceptions: [
        {
          url: '/integration-test-result',
          replaceTo: '/pages/integrations'
        },
        {
          url: '/gitbook',
          replaceTo: '/pages/integrations'
        },
      ],
      lastUnknownLocationExceptions: ['/', '/pages']
    }),
    AppRoutingModule,
    SwuiTopMenuModule,
    SwuiGridModule.forRoot({
      widgets: [
        { type: 'tdgameFinish', component: TDGameFinishWidget },
        { type: 'tdbrokenGameFinish', component: TDBrokenGameFinishWidget },
        { type: 'tdradioButton', component: TDRadioButtonWidget },
        { type: 'tdactionList', component: TdActionListWidget },
        { type: 'tdissue', component: IssueWidgetComponent },
      ],
      widgetAddType: 'EXPAND'
    }),
    SwuiNotificationsModule.forRoot(),
    GameFinishWidgetModule,
    BrokenGameFinishWidgetModule,
    RadioButtonWidgetModule,
    ActionListWidgetModule,
    IssueWidgetModule,
    SwDexieModule.forRoot('SwUboHubCasino'),
  ],
  providers: [
    SwBrowserTitleService,
    SwHubConfigService,
    { provide: APP_INITIALIZER, useFactory: hub_config_init, deps: [SwHubConfigService], multi: true },
    EntityService,
    EntityDataSourceService,
    { provide: SwHubEntityDataSource, useExisting: EntityDataSourceService },
    GlobalState,
    UserService,
    BiService,
    BaThemeSpinner,
    SwHubAuthService,
    { provide: ColumnsManagementDataProvider, useClass: SwDexieColumnsDataProviderService }
  ],
})
export class AppModule {
  static injector;

  constructor( injector: Injector ) {
    AppModule.injector = injector;
  }
}
