import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { EMPTY, Observable } from 'rxjs';
import { catchError, shareReplay } from 'rxjs/operators';
import { ServerConfig } from '../../typings/server-config';

@Injectable()
export class ConfigResolver implements Resolve<ServerConfig> {
  private cachedConfig$: Observable<ServerConfig>;

  constructor(private http: HttpClient) {
  }

  resolve() {
    if (!this.cachedConfig$) {
      this.cachedConfig$ = this.http.get<ServerConfig>('/api/config').pipe(
        shareReplay(1),
        catchError(err => {
          console.error(err);
          this.cachedConfig$ = null;
          return EMPTY;
        })
      );
    }
    return this.cachedConfig$;
  }
}
