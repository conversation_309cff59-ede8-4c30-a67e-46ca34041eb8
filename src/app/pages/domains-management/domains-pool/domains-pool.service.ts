import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { BehaviorSubject, Observable, Subject, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { API_ENDPOINT, ZERO_TIME } from '../../../app.constants';
import { DomainPool, DomainPoolData, DomainPoolRow, DomainType } from '../../../common/models/domain.model';

export function toDomainPool(type: DomainType) {
  return function (record?: DomainPoolRow): DomainPool {
    if (!record) {
      return undefined;
    }
    for (const property of ['createdAt', 'updatedAt']) {
      if (record[property] === ZERO_TIME) {
        record[property] = null;
      }
    }
    return {
      ...record,
      type,
      _meta: {
        createdAt: record.createdAt && moment(record.createdAt),
        updatedAt: record.updatedAt && moment(record.updatedAt),
      }
    };
  };
}

@Injectable()
export class DomainsPoolService {
  items = new BehaviorSubject<DomainPool[]>([]);
  isGridChanged$ = new Subject<boolean>();

  constructor(private readonly http: HttpClient, private readonly notifications: SwuiNotificationsService) {
  }

  getAll() {
    const staticPools$ = this.getList('static', undefined, false);
    const dynamicPools$ = this.getList('dynamic', undefined, false);

    return staticPools$.pipe(
      switchMap(staticPools =>
        dynamicPools$.pipe(
          map(dynamicPools => [...staticPools, ...dynamicPools])
        )
      )
    );
  }

  getList(type: DomainType, path?: string, force?: boolean): Observable<DomainPool[]> {
    if (!force) {
    }
    return this.http.get<DomainPoolRow[]>(`${this.getUrl({ path, type })}`)
      .pipe(
        map(response => response.map(toDomainPool(type))),
        tap(data => {
          this.items.next(data);
        }),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  delete(type: DomainType, id: string): Observable<Object> {
    return this.http.delete(this.getUrl({ id, type }))
      .pipe(
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  create(type: DomainType, data: DomainPool): Observable<DomainPool> {
    if ('_meta' in data) {
      delete data['_meta'];
    }
    return this.http.post<DomainPoolRow>(this.getUrl({ type }), data)
      .pipe(
        map(toDomainPool(type)),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  update(type: DomainType, id: string, data: DomainPoolData): Observable<DomainPool> {
    if ('_meta' in data) {
      delete data['_meta'];
    }
    return this.http.patch<DomainPoolRow>(this.getUrl({ id, type }), data)
      .pipe(
        map(toDomainPool(type)),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  private getUrl({ type, id, path }: { path?: string, id?: string, type: DomainType }): string {
    return `${API_ENDPOINT}${path && path !== ':' ? `/entities/${path}` : ''}/domain-pools${id ? `/${id}` : ''}/${type}`;
  }
}
