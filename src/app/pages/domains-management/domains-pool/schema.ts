import { SwuiGridField } from '@skywind-group/lib-swui';

const DOMAIN_TYPE_MAP = [
  { id: 'static', code: 'static', displayName: 'DOMAINS.GRID.typeStatic' },
  { id: 'dynamic', code: 'dynamic', displayName: 'DOMAINS.GRID.typeDynamic' },
];

const getSchema = (domainWatcherAdapters: string[]): SwuiGridField[] => [
  {
    field: 'name',
    title: 'DOMAINS.GRID.name',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false,
  },
  {
    field: 'type',
    title: 'DOMAINS.GRID.poolType',
    type: 'select',
    data: DOMAIN_TYPE_MAP,
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    filter: {
      field: 'type',
      type: 'select',
    },
    emptyOption: {
      show: false,
    },
    td: {
      type: 'calc',
      titleFn: (row: any, schema: SwuiGridField) => DOMAIN_TYPE_MAP.find(({ id }) => id === row[schema.field])?.displayName,
      classFn: () => 'sw-chip sw-chip-blue'
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  ...(domainWatcherAdapters.length ? [{
    field: 'domainWatcherAdapterId',
    title: 'DOMAINS.GRID.domainWatcherAdapter',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: true,
    td: {
      type: 'calc',
      titleFn: (row: any) => row.domainWatcherAdapterId || '-'
    },
  }] : []),
  {
    field: 'createdAt',
    title: 'DOMAINS.GRID.created',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: false,
    isSortable: true,
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
  {
    field: 'updatedAt',
    title: 'DOMAINS.GRID.updated',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: true,
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
];

export const getSchemaList = (domainWatcherAdapters: string[]): SwuiGridField[] => {
  const schema = getSchema(domainWatcherAdapters);
  return schema.filter(el => el.isList).map(el => {
    el.isListVisible = true;
    return el;
  });
};
