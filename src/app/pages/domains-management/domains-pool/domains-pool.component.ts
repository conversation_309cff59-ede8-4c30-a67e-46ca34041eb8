import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { RowAction, SettingsService, SwuiGridComponent, SwuiGridField, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { DEFAULT_PAGE_SIZE } from '../../../app.constants';
import { Domain, DomainPool } from '../../../common/models/domain.model';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';
import { DomainsPoolDialogComponent } from './dialog/domains-pool-dialog.component';
import { DomainsPoolService } from './domains-pool.service';
import { getSchemaList } from './schema';

@Component({
  selector: 'domains-pool',
  templateUrl: './domains-pool.component.html',
})
export class DomainsPoolComponent implements OnInit, OnDestroy {
  readonly schema: SwuiGridField[];
  readonly domainWatcherAdapters: string[];
  pageSize = DEFAULT_PAGE_SIZE;

  rowActions: RowAction[] = [];
  data: DomainPool[];

  @ViewChild('grid', { static: true }) public grid: SwuiGridComponent<Domain>;

  private _destroyed$ = new Subject();

  constructor(private service: DomainsPoolService,
    private settingsService: SettingsService,
    private dialog: MatDialog,
    private notifications: SwuiNotificationsService,
    private translate: TranslateService,
    { snapshot: { data: { config } } }: ActivatedRoute,
  ) {
    this.domainWatcherAdapters = config?.domainWatcherAdapters ?? [];
    this.schema = getSchemaList(this.domainWatcherAdapters);

    this.service.isGridChanged$
      .pipe(
        switchMap(() => this.service.getAll()),
        takeUntil(this._destroyed$)
      )
      .subscribe((val: DomainPool[]) => {
        this.data = val;
      });
  }

  ngOnInit() {
    this.settingsService.appSettings$.pipe(takeUntil(this._destroyed$)).subscribe(val => {
      this.pageSize = val.pageSize;
    });

    this.service.getAll().pipe(takeUntil(this._destroyed$)).subscribe((val: DomainPool[]) => {
      this.data = val;
    });
    this.setRowActions();
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  refreshGrid() {
    this.service.isGridChanged$.next();
  }

  private setRowActions() {
    this.rowActions = [
      {
        title: 'DOMAINS.GRID.editPool',
        icon: 'edit',
        fn: (item: DomainPool) => {
          const dialogRef = this.dialog.open(DomainsPoolDialogComponent, {
            width: '500px',
            data: {
              pool: item,
              poolType: item.type,
              domainWatcherAdapters: this.domainWatcherAdapters
            },
            disableClose: true
          });

          dialogRef.afterClosed()
            .pipe(
              filter(Boolean),
              switchMap((domainPool) => this.service.update(item.type, item.id, domainPool)),
              switchMap(domainPool => this.translate.get('DOMAINS.notificationPoolModified', { name: domainPool.name })),
              tap((message) => this.notifications.success(message)),
              switchMap(() => this.service.getAll()),
              takeUntil(this._destroyed$)
            )
            .subscribe(val => {
              this.data = val;
            });
        },
        canActivateFn: () => true,
      },
      {
        title: 'DOMAINS.GRID.removePool',
        icon: 'delete',
        fn: (item: DomainPool) => {
          const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            width: '500px',
            data: item,
            disableClose: true
          });

          dialogRef.afterClosed()
            .pipe(
              filter(data => !!data),
              switchMap(() => this.service.delete(item.type, item.id)),
              switchMap(() => this.translate.get('DOMAINS.notificationPoolRemoved', { name: item.name })),
              tap((message) => this.notifications.success(message)),
              switchMap(() => this.service.getAll()),
              takeUntil(this._destroyed$)
            )
            .subscribe((val: DomainPool[]) => this.data = val);
        },
        canActivateFn: () => true,
      },
    ];
  }
}
