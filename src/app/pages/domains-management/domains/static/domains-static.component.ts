import { Component } from '@angular/core';
import { SwuiGridDataService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';
import { DOMAIN_TYPES } from '../../../../common/models/domain.model';
import { SCHEMA_FILTER, SCHEMA_LIST } from './schema';
import { DomainsManagementService } from '../../domains-management.service';
import { DomainsComponent } from '../domains.component';

@Component({
  selector: 'domains-static',
  templateUrl: '../domains.component.html',
  providers: [
    SwuiTopFilterDataService,
    { provide: SwuiGridDataService, useExisting: DomainsManagementService }
  ],
})
export class DomainsStaticComponent extends DomainsComponent {
  readonly schema = SCHEMA_LIST;
  readonly filterSchema = SCHEMA_FILTER;
  readonly domainType = DOMAIN_TYPES.static;
}
