import { SwuiGridField } from '@skywind-group/lib-swui';
import { DOMAIN_NAME_SCHEMA, DOMAIN_SCHEMA } from '../schema';

export const DOMAIN_TYPE_MAP = [
  { id: 'static', code: 'static', displayName: 'DOMAINS.GRID.typeStatic' },
  { id: 'lobby', code: 'lobby', displayName: 'DOMAINS.GRID.typeLobby' },
  { id: 'live-streaming', code: 'live-streaming', displayName: 'DOMAINS.GRID.typeLiveStreaming' },
  { id: 'ehub', code: 'ehub', displayName: 'DOMAINS.GRID.typeEhub' },
];

const SCHEMA: SwuiGridField[] = [
  DOMAIN_NAME_SCHEMA,
  {
    field: 'type',
    title: 'DOMAINS.GRID.type',
    type: 'select',
    data: DOMAIN_TYPE_MAP,
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    filter: {
      field: 'type',
      type: 'select',
    },
    emptyOption: {
      show: false,
    },
    td: {
      type: 'calc',
      titleFn: (row: any, schema: SwuiGridField) => DOMAIN_TYPE_MAP.find(({ id }) => id === row[schema.field])?.displayName,
      classFn: () => 'sw-chip sw-chip-blue'
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  ...DOMAIN_SCHEMA
];

export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
export const SCHEMA_LIST = SCHEMA.filter(el => el.isList).map(el => ({ ...el, isListVisible: true }));
