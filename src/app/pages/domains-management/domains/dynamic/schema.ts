import { SwuiGridField } from '@skywind-group/lib-swui';
import { DOMAIN_NAME_SCHEMA, DOMAIN_SCHEMA } from '../schema';

const SCHEMA: SwuiGridField[] = [
  DOMAIN_NAME_SCHEMA,
  {
    field: 'environment',
    title: 'DOMAINS.GRID.environment',
    type: 'string',
    isList: true,
    isViewable: false,
    isSortable: false
  },
  ...DOMAIN_SCHEMA
];

export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
export const SCHEMA_LIST = SCHEMA.filter(el => el.isList).map(el => ({ ...el, isListVisible: true }));
