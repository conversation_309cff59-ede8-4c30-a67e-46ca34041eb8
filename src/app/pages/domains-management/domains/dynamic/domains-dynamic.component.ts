import { Component } from '@angular/core';
import { SwuiGridDataService, SwuiTopFilterDataService } from '@skywind-group/lib-swui';
import { DOMAIN_TYPES } from '../../../../common/models/domain.model';
import { DomainsManagementService } from '../../domains-management.service';
import { SCHEMA_FILTER, SCHEMA_LIST } from './schema';
import { DomainsComponent } from '../domains.component';

@Component({
  selector: 'domains-dynamic',
  templateUrl: '../domains.component.html',
  providers: [
    SwuiTopFilterDataService,
    { provide: SwuiGridDataService, useExisting: DomainsManagementService }
  ],
})
export class DomainsDynamicComponent extends DomainsComponent {
  readonly schema = SCHEMA_LIST;
  readonly filterSchema = SCHEMA_FILTER;
  readonly domainType = DOMAIN_TYPES.dynamic;
}
