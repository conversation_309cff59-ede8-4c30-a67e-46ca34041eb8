import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Domain, DOMAIN_TYPES, DomainsItemDialogData, DomainType, STATIC_DOMAIN_TYPES } from '../../../../common/models/domain.model';
import { DOMAIN_TYPE_MAP } from '../static/schema';
import { GameServerService } from '../../../game-server/game-server.service';

@Component({
  selector: 'domains-item-dialog',
  templateUrl: 'domains-item-dialog.component.html',
})
export class DomainsItemDialogComponent implements OnInit {
  readonly domainType: DomainType;
  readonly title: string;
  readonly domain?: Domain;
  readonly gameServers: string[] = [];
  readonly staticTypes = DOMAIN_TYPE_MAP;
  readonly form: FormGroup;

  constructor(private dialogRef: MatDialogRef<DomainsItemDialogComponent>,
    private fb: FormBuilder,
    private gameServerService: GameServerService,
    @Inject(MAT_DIALOG_DATA) { type, domain }: DomainsItemDialogData) {
    this.domainType = type;

    let typeName = 'Dynamic';
    if (type === DOMAIN_TYPES.static) {
      typeName = 'Static';
    }
    this.title = `DOMAINS.${(domain?.id ? 'edit' : 'add')}${typeName}Domain`;

    this.form = this.fb.group({
      domain: [domain?.domain ?? '', Validators.required],
      description: [domain?.description ?? ''],
      expiryDate: [domain?.expiryDate ? new Date(domain.expiryDate) : null]
    });

    if (type === DOMAIN_TYPES.dynamic) {
      const control = this.fb.control(domain?.environment ?? '');
      control.setValidators(Validators.required);
      this.form.addControl('environment', control);

      if (this.staticTypeControl) {
        this.form.removeControl('type');
      }

      this.form.updateValueAndValidity();
    } else {
      const control = this.fb.control(domain?.type ?? STATIC_DOMAIN_TYPES.static);
      control.setValidators(Validators.required);
      this.form.addControl('type', control);

      if (this.environmentControl) {
        this.form.removeControl('environment');
        this.form.updateValueAndValidity();
      }
    }
  }

  ngOnInit() {
    this.gameServerService.getList().subscribe(gameServers => {
      this.gameServers.length = 0; // Clear the array
      this.gameServers.push(...gameServers.map(({ name }) => name));
    });
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  submit() {
    this.form.markAllAsTouched();
    if (this.form.valid) {
      const formValue = { ...this.form.value };
      if (!formValue.type) {
        delete formValue.type;
      }
      if (!formValue.environment) {
        delete formValue.environment;
      }
      if (!formValue.description) {
        delete formValue.description;
      }
      if (!formValue.expiryDate) {
        delete formValue.expiryDate;
      }
      this.dialogRef.close(formValue);
    }
  }

  get domainControl(): FormControl {
    return this.form.get('domain') as FormControl;
  }

  get staticTypeControl(): FormControl {
    return this.form.get('type') as FormControl;
  }

  get environmentControl(): FormControl {
    return this.form.get('environment') as FormControl;
  }

  get descriptionControl(): FormControl {
    return this.form.get('description') as FormControl;
  }

  get expiryDateControl(): FormControl {
    return this.form.get('expiryDate') as FormControl;
  }
}
