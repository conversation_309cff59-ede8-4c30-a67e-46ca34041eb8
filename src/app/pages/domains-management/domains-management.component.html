<lib-swui-page-panel [title]="'DOMAINS.title'" [actions]="panelActions"></lib-swui-page-panel>
<div class="p-32">
  <mat-tab-group (selectedTabChange)="onTabChange($event)" [animationDuration]="0">
    <mat-tab>
      <ng-template mat-tab-label>{{ 'DOMAINS.domainPools' | translate }}</ng-template>
      <domains-pool></domains-pool>
    </mat-tab>
    <mat-tab>
      <ng-template mat-tab-label>{{ 'DOMAINS.staticDomains' | translate }}</ng-template>
      <domains-static></domains-static>
    </mat-tab>
    <mat-tab>
      <ng-template mat-tab-label>{{ 'DOMAINS.dynamicDomains' | translate }}</ng-template>
      <domains-dynamic></domains-dynamic>
    </mat-tab>
  </mat-tab-group>
</div>
