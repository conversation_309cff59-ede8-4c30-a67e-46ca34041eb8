import { HttpClient, HttpParams } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { SwuiGridDataService } from '@skywind-group/lib-swui';
import { tap } from 'rxjs/operators';

import { API_ENDPOINT } from '../../../../../../app.constants';
import { BaseApiObject } from '../../../../../../common/typings';


export abstract class AbstractCustomerGameHistoryService<T extends BaseApiObject> implements SwuiGridDataService<T> {
  path: string;
  url: string;
  id: string;
  games: { id: string; text: string; }[];

  protected constructor(
    readonly route: ActivatedRoute,
    readonly http: HttpClient,
    readonly historyUrl: string,
  ) {
    const { snapshot: { params: { path: playerPath, id: playerId }, data: { gamesShortInfo } } } = this.route;
    this.path = playerPath;
    this.url = historyUrl;
    this.id = playerId;
    this.games = gamesShortInfo || [];
  }

  getUrl( path?: string, urlList?: string ): string {
    return `${API_ENDPOINT}${path ? '/entities/' + path : ''}${urlList}`;
  }

  getGridData(params: HttpParams) {
    const roundId = params.get('roundId__in') || params.get('roundId');
    if (!roundId) {
      params = params.set('playerCode', this.id);
    }

    return this.http.get<T[]>(this.getUrl(this.path, this.url), {
      params,
      observe: 'response'
    }).pipe(
      tap(resp => resp.body.forEach(( player: T ) => player._meta = { fullPath: this.path })),
      tap(response => this.processRecord(response.body))
    );
  }

  private processRecord( gameHistory: any[] ) {
    if (gameHistory) {
      gameHistory.forEach(history => {
          const gameInfo = this.games.find(game => game.id === history.gameCode);
          history.gameNameLabel = gameInfo ? gameInfo.text : '';
          history.recoveryType = history.recoveryType !== null ? history.recoveryType : null;
          history.outcome = history.bet > history.win ? 'LOSE' :
            (history.bet === history.win ? (history.recoveryType === 'revert' ? 'VOID' : 'TIE') : 'WIN');
          if (history.bet !== 0) {
            history.ggrPerc = (history.revenue / history.bet * 100).toFixed(2);
          }
        }
      );
    }

    return gameHistory;
  }
}
