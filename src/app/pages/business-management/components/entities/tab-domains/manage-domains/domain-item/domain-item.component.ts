import { Component, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { filter, finalize, switchMap, take } from 'rxjs/operators';
import { EntityDomainService } from 'src/app/pages/domains-management/entity-domain.service';
import { Domain, DOMAIN_TYPES, DomainType, STATIC_DOMAIN_TYPES, StaticDomainType } from '../../../../../../../common/models/domain.model';
import { Entity } from '../../../../../../../common/models/entity.model';
import { SelectDomainDialogComponent } from '../select-domain-dialog.component';

@Component({
  selector: 'domain-item',
  templateUrl: './domain-item.component.html',
  styleUrls: [
    './domain-item.component.scss',
  ],
})
export class DomainItemComponent {
  readonly domainTypes = DOMAIN_TYPES;

  @Input() domainType: DomainType;
  @Input() staticDomainType: StaticDomainType;
  @Input() entity: Entity;

  loading = true;

  private _domain: Domain | null;
  private resetComplete: boolean = false;

  constructor(
    private readonly entityDomainService: EntityDomainService,
    private readonly dialog: MatDialog,
  ) {
  }

  get domain(): Domain | null {
    let domainId: string;
    if (this.domainType === DOMAIN_TYPES.static) {
      if (this.staticDomainType === STATIC_DOMAIN_TYPES.static) {
        domainId = this.entity.staticDomainId;
      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.lobby) {
        domainId = this.entity.lobbyDomainId;
      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.liveStreaming) {
        domainId = this.entity.liveStreamingDomainId;
      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.ehub) {
        domainId = this.entity.ehubDomainId;
      }
    } else {
      domainId = this.entity.dynamicDomainId;
    }
    if (this._domain && this._domain.id !== domainId) {
      return { ...this._domain, inherited: true };
    }
    return this._domain;
  }

  set domain(domain: Domain | null) {
    this._domain = domain;
  }

  ngOnInit() {
    this.loadDomain();
  }

  resetButtonDisabled(): boolean {
    return !(this.domain && this.domain.hasOwnProperty('id')) || this.resetComplete || this.domain.inherited;
  }

  resetToParent(event: Event) {
    event.preventDefault();
    if (this.resetButtonDisabled()) {
      return;
    }
    this.entityDomainService.removeEntityDomain(this.domainType, this.entity.path, this.staticDomainType).pipe(
      take(1),
    ).subscribe(() => {
      this.setEntity(null);
      this.loadDomain();
      this.resetComplete = true;
    });
  }

  setNewDomain() {
    this.dialog.open(SelectDomainDialogComponent, {
      width: '600px',
      data: {
        domainType: this.domainType,
        staticDomainType: this.staticDomainType,
        domainId: this.domain?.id,
      },
      disableClose: true
    }).afterClosed().pipe(
      filter(result => !!result),
      switchMap((domain) => this.entityDomainService.setEntityDomain(this.domainType, domain.id, this.entity.path, this.staticDomainType)),
      take(1),
    ).subscribe((domain) => {
      this.setEntity(domain);
      this.loadDomain();
      this.resetComplete = false;
    });
  }

  private setEntity(domain: Domain | null) {
    if (this.domainType === DOMAIN_TYPES.static) {
      if (this.staticDomainType === STATIC_DOMAIN_TYPES.static) {
        this.entity.staticDomainId = domain?.id;
      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.lobby) {
        this.entity.lobbyDomainId = domain?.id;
      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.liveStreaming) {
        this.entity.liveStreamingDomainId = domain?.id;
      } else if (this.staticDomainType === STATIC_DOMAIN_TYPES.ehub) {
        this.entity.ehubDomainId = domain?.id;
      }
    } else {
      this.entity.dynamicDomainId = domain?.id;
    }
  }

  private loadDomain() {
    this.loading = true;
    this.entityDomainService.getEntityDomain(this.domainType, this.entity.path, true, this.staticDomainType).pipe(
      finalize(() => this.loading = false),
      take(1)
    ).subscribe((domain) => {
      this.domain = domain;
    });
  }
}
