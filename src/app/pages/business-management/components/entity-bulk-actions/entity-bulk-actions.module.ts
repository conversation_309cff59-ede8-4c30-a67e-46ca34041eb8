import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiInputModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { TableVirtualScrollModule } from 'ng-table-virtual-scroll';

import { ControlMessagesModule } from '../../../../common/components/control-messages/control-messages.module';
import { TrimInputValueModule } from '../../../../common/directives/trim-input-value/trim-input-value.module';
import { CsvService } from '../../../../common/services/csv.service';
import { DynamicDomainsResolver } from '../../../../common/services/resolvers/dynamic-domains.resolver';
import { DynamicEntitydomainResolver } from '../../../../common/services/resolvers/dynamic-entitydomain.resolver';
import { StaticDomainsResolver } from '../../../../common/services/resolvers/static-domains.resolver';
import { StaticEntityDomainResolver } from '../../../../common/services/resolvers/static-entity-domain-resolver.service';
import { StructureResolver } from '../../../../common/services/resolvers/structure.resolver';
import { DomainsManagementService } from '../../../domains-management/domains-management.service';
import { EntityDomainService } from '../../../domains-management/entity-domain.service';
import { ActionEditComponent } from './action-edit/action-edit.component';
import { EntityBulkActionsComponent } from './entity-bulk-actions.component';
import { EntityBulkActionsRoutingModule } from './entity-bulk-actions.routing';
import { EntityBulkActionsService } from './entity-bulk-actions.service';
import { EntityLinkerComponent } from './entity-linker/entity-linker.component';
import { DomainsListComponent } from './switch-domain/domains-list/domains-list.component';
import { SwitchDomainComponent } from './switch-domain/switch-domain.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    EntityBulkActionsRoutingModule,
    TranslateModule,
    SwuiPagePanelModule,
    MatCardModule,
    MatTabsModule,
    ScrollingModule,
    MatSelectModule,
    MatIconModule,
    ControlMessagesModule,
    SwuiInputModule,
    MatTableModule,
    MatDialogModule,
    MatButtonModule,
    MatRadioModule,
    MatProgressSpinnerModule,
    MatInputModule,
    MatSortModule,
    MatCheckboxModule,
    MatTooltipModule,
    TableVirtualScrollModule,
    TrimInputValueModule
  ],
  exports: [EntityBulkActionsComponent],
  declarations: [
    EntityBulkActionsComponent,
    ActionEditComponent,
    EntityLinkerComponent,
    SwitchDomainComponent,
    DomainsListComponent
  ],
  providers: [
    StructureResolver,
    DynamicDomainsResolver,
    StaticDomainsResolver,
    DynamicEntitydomainResolver,
    StaticEntityDomainResolver,
    DomainsManagementService,
    EntityDomainService,
    CsvService,
    EntityBulkActionsService,
  ],
})
export class EntityBulkActionsModule {
}
