import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, Inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'action-edit',
  templateUrl: './action-edit.component.html',
  styleUrls: ['./action-edit.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActionEditComponent implements OnInit, AfterViewInit, OnDestroy {
  path = '';
  dataSource: MatTableDataSource<string>;
  foundDomainsList = [];
  searchControl = new FormControl('');
  selectedDomain?;

  @ViewChild('tableWrapper', { read: ElementRef }) tableWrapper: ElementRef;

  private domainsList = [];
  private destroy$ = new Subject();

  constructor(@Inject(MAT_DIALOG_DATA) public dialogData,
    public dialogRef: MatDialogRef<ActionEditComponent>
  ) {
  }

  ngOnInit() {
    this.path = this.dialogData.item?.fullPath.join(' / ') || 'Selected Items';
    this.domainsList = this.getDomains();
    this.foundDomainsList = this.domainsList;
    this.selectedDomain = this.getSelectedDomain();

    this.searchControl.valueChanges
      .pipe(
        map(value => value.toLowerCase()),
        takeUntil(this.destroy$)
      )
      .subscribe(value => {
        this.selectedDomain = null;
        this.foundDomainsList = this.domainsList.filter(({ environment, domain }) => {
          return (environment || '').toLowerCase().includes(value) || (domain || '').toLowerCase().includes(value);
        });
      });
  }

  get selectedIndex() {
    return this.foundDomainsList.indexOf(this.selectedDomain);
  }

  ngAfterViewInit() {
    this.tableWrapper.nativeElement.scrollTo(0, 48 * this.selectedIndex);
  }

  selectDomain(index) {
    this.selectedDomain = this.foundDomainsList[index];
  }

  submit() {
    this.dialogRef.close(this.selectedDomain.id);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getDomains() {
    return this.dialogData?.domains;
  }

  private getSelectedDomain() {
    if (this.dialogData.multiple || this.dialogData.domain.inherited) {
      return null;
    }

    let index = 0;

    for (let i = 0; i < this.foundDomainsList.length; i++) {
      if (this.foundDomainsList[i].id === this.dialogData.domain.id) {
        index = i;
        break;
      }
    }

    return this.foundDomainsList[index];
  }
}
