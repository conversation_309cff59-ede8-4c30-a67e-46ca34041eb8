import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { Domain } from '../../../../../../common/models/domain.model';

@Component({
  selector: 'domains-list',
  templateUrl: './domains-list.component.html',
  styleUrls: ['./domains-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DomainsListComponent implements OnInit, OnDestroy {
  @Input() title: string;
  @Input() domainsList: Domain[] = [];
  @Input() componentName: string;
  @Output() onSelect = new EventEmitter<Domain | null>();

  dataSource: MatTableDataSource<string>;
  foundDomainsList: Domain[] = [];
  searchControl = new FormControl('');

  set selectedDomain(domain: Domain | null) {
    this._selectedDomain = domain;
    this.onSelect.emit(domain);
  }

  get selectedDomain() {
    return this._selectedDomain;
  }

  private _selectedDomain: Domain | null;
  private readonly destroy$ = new Subject();

  ngOnInit(): void {
    this.foundDomainsList = this.domainsList;

    this.searchControl.valueChanges
      .pipe(
        map(value => value.toLowerCase()),
        takeUntil(this.destroy$)
      )
      .subscribe(value => {
        this.selectedDomain = null;
        this.foundDomainsList = this.domainsList.filter(({ environment, domain }) => {
          return ((environment || '').toLowerCase().includes(value) || (domain || '').toLowerCase().includes(value));
        });
      });
  }

  selectDomain(index: number) {
    this.selectedDomain = this.foundDomainsList[index];
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
