{"compilerOptions": {"downlevelIteration": true, "target": "es2015", "module": "es2020", "moduleResolution": "node", "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "allowUnusedLabels": true, "noImplicitAny": false, "noImplicitThis": false, "noImplicitReturns": false, "noUnusedParameters": true, "noUnusedLocals": true, "sourceMap": true, "noEmitHelpers": true, "importHelpers": true, "strictNullChecks": false, "baseUrl": "./", "lib": ["es2015", "dom"], "typeRoots": ["node_modules/@types"]}, "awesomeTypescriptLoaderOptions": {"forkChecker": true, "useWebpackText": true}, "angularCompilerOptions": {"genDir": "./compiled", "skipMetadataEmit": true}, "compileOnSave": false, "buildOnSave": false, "atom": {"rewriteTsconfig": false}}